#!/usr/bin/env python3
"""
Script to check file existence for Immich data export.
Reads CSV file and checks:
1. originalPath file existence
2. For missing IMAGE files: checks preview files in /media/Data/@mydata/Images/thumbs/{uuid}/{xx}/{yy}/{id}-preview.jpeg
3. For missing VIDEO files: checks encodedVideoPath if available
"""

import csv
import os
import sys
from pathlib import Path

def get_preview_path(file_id, original_filename):
    """
    Generate preview path based on ID pattern:
    /media/Data/@mydata/Images/thumbs/{uuid}/{xx}/{yy}/{id}-preview.jpeg
    where xx and yy are derived from the first 4 characters of the ID.
    """
    if len(file_id) < 4:
        return None
    
    # Extract first 4 characters and split into xx and yy
    first_four = file_id[:4]
    xx = first_four[:2]
    yy = first_four[2:4]
    
    # Use original filename but change extension to .jpeg
    if original_filename:
        # Get filename without extension and add -preview.jpeg
        base_name = Path(original_filename).stem
        preview_filename = f"{base_name}-preview.jpeg"
    else:
        # Fallback to ID-based naming
        preview_filename = f"{file_id}-preview.jpeg"
    
    preview_path = f"/media/Data/@mydata/Images/thumbs/{file_id}/{xx}/{yy}/{preview_filename}"
    return preview_path

def check_file_existence(csv_file_path, dry_run=True, max_rows=None):
    """
    Check file existence for each row in the CSV file.

    Args:
        csv_file_path: Path to the CSV file
        dry_run: If True, only print what would be checked without actually checking files
        max_rows: If specified, limit processing to this many rows (useful for testing)
    """
    
    if not os.path.exists(csv_file_path):
        print(f"ERROR: CSV file not found: {csv_file_path}")
        return
    
    print(f"Reading CSV file: {csv_file_path}")
    print("=" * 80)
    
    total_rows = 0
    missing_original = 0
    found_preview = 0
    found_encoded = 0
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)

            for row_num, row in enumerate(reader, start=2):  # Start at 2 because header is row 1
                total_rows += 1

                # Check if we've reached the maximum number of rows to process
                if max_rows and total_rows > max_rows:
                    print(f"\nReached maximum rows limit ({max_rows}). Stopping processing.")
                    break
                
                file_id = row.get('id', '').strip('"')
                file_type = row.get('type', '').strip('"')
                original_path = row.get('originalPath', '').strip('"')
                encoded_video_path = row.get('encodedVideoPath', '').strip('"')
                original_filename = row.get('originalFileName', '').strip('"')
                
                print(f"\nRow {row_num}: ID={file_id}")
                print(f"  Type: {file_type}")
                print(f"  Original Path: {original_path}")
                print(f"  Original Filename: {original_filename}")
                
                # Check original file
                original_exists = False
                if original_path:
                    if dry_run:
                        print(f"  [DRY RUN] Would check: {original_path}")
                        # For dry run, simulate some missing files for demonstration
                        original_exists = (row_num % 3 != 0)  # Simulate 1/3 missing
                    else:
                        original_exists = os.path.exists(original_path)
                    
                    if original_exists:
                        print(f"  ✓ Original file EXISTS: {original_path}")
                    else:
                        print(f"  ✗ Original file MISSING: {original_path}")
                        missing_original += 1
                else:
                    print(f"  ⚠ No original path specified")
                
                # If original file is missing, check alternatives
                if not original_exists:
                    if file_type.upper() == 'IMAGE':
                        # Check preview file
                        preview_path = get_preview_path(file_id, original_filename)
                        if preview_path:
                            if dry_run:
                                print(f"  [DRY RUN] Would check preview: {preview_path}")
                                # Simulate some preview files found
                                preview_exists = (row_num % 5 == 0)
                            else:
                                preview_exists = os.path.exists(preview_path)
                            
                            if preview_exists:
                                print(f"  ✓ Preview file FOUND: {preview_path}")
                                found_preview += 1
                            else:
                                print(f"  ✗ Preview file MISSING: {preview_path}")
                        else:
                            print(f"  ⚠ Could not generate preview path for ID: {file_id}")
                    
                    elif file_type.upper() == 'VIDEO':
                        # Check encoded video path if available
                        if encoded_video_path:
                            if dry_run:
                                print(f"  [DRY RUN] Would check encoded video: {encoded_video_path}")
                                # Simulate some encoded videos found
                                encoded_exists = (row_num % 4 == 0)
                            else:
                                encoded_exists = os.path.exists(encoded_video_path)
                            
                            if encoded_exists:
                                print(f"  ✓ Encoded video FOUND: {encoded_video_path}")
                                found_encoded += 1
                            else:
                                print(f"  ✗ Encoded video MISSING: {encoded_video_path}")
                        else:
                            print(f"  ⚠ No encoded video path available")
                
                # Add separator for readability
                print("-" * 60)
                
                # Progress indicator for large files
                if total_rows % 100 == 0:
                    print(f"\nProcessed {total_rows} rows...")
    
    except Exception as e:
        print(f"ERROR reading CSV file: {e}")
        return
    
    # Print summary report
    print("\n" + "=" * 80)
    print("SUMMARY REPORT")
    print("=" * 80)
    print(f"Total rows processed: {total_rows}")
    print(f"Missing original files: {missing_original}")
    print(f"Preview files found: {found_preview}")
    print(f"Encoded videos found: {found_encoded}")
    
    if total_rows > 0:
        missing_percentage = (missing_original / total_rows) * 100
        print(f"Missing percentage: {missing_percentage:.1f}%")
    
    print("\nFile Status Legend:")
    print("  ✓ = File exists")
    print("  ✗ = File missing")
    print("  ⚠ = Warning/issue")

def main():
    """Main function to run the file checker."""

    # Default CSV file path
    default_csv = "immich/data-1750883770936.csv"

    # Check for flags
    dry_run = "--dry-run" in sys.argv or "-n" in sys.argv

    # Check for max rows limit
    max_rows = None
    for arg in sys.argv:
        if arg.startswith("--max-rows="):
            try:
                max_rows = int(arg.split("=")[1])
            except ValueError:
                print(f"Invalid max-rows value: {arg}")
                return

    # Filter out flags to get the CSV file argument
    args = [arg for arg in sys.argv[1:] if not arg.startswith('-')]

    # Check command line arguments
    if len(args) > 0:
        csv_file = args[0]
    else:
        csv_file = default_csv

    if dry_run:
        print("DRY RUN MODE: Will simulate file checks without actually accessing files")
        print("Use without --dry-run or -n flag to perform actual file checks")
        print()

    if max_rows:
        print(f"Processing limited to {max_rows} rows")
        print()

    check_file_existence(csv_file, dry_run=dry_run, max_rows=max_rows)

if __name__ == "__main__":
    main()
