#!/bin/bash

# API_KEY="aS8tBo6eaWO4NKpBN049e1cbKfH3cG7H71w4Hggxo" #hai
API_KEY="Tqap0OuXDNq28IqIidXemqh0wVFwaKhHRdoS5KwciU" #quynh
INPUT_FILE="extracted_ids.txt"
DELETE_URL="https://image.haivita.top/api/assets"
BATCH_SIZE=500
FORCE_DELETE=false

if [ ! -f "$INPUT_FILE" ]; then
  echo "UUID file $INPUT_FILE not found!"
  exit 1
fi

mapfile -t UUID_ARRAY < "$INPUT_FILE"
TOTAL=${#UUID_ARRAY[@]}
echo "Total assets to delete: $TOTAL"

for ((i=0; i<TOTAL; i+=BATCH_SIZE)); do
  echo "Deleting batch $((i / BATCH_SIZE + 1))..."

  # Build batch JSON array
  CHUNK=$(printf '%s\n' "${UUID_ARRAY[@]:i:BATCH_SIZE}" | jq -R -s -c 'split("\n")[:-1]')

  # Perform DELETE request
  curl --silent --location --request DELETE "$DELETE_URL" \
    --header "x-api-key: $API_KEY" \
    --header "Content-Type: application/json" \
    --data-raw "{\"force\":$FORCE_DELETE,\"ids\": $CHUNK}"

  echo "Done batch $((i / BATCH_SIZE + 1))"
done

echo "✅ All done."
