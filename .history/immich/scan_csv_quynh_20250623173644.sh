#!/bin/bash

CSV_FILE="data-1750674776329.csv"
COPY=false

[[ "$*" == *--copy* ]] && COPY=true
[[ "$COPY" == false ]] && echo "ℹ️ Dry run mode (use --copy to actually copy files)"

tail -n +2 "$CSV_FILE" | while IFS=',' read -r id originalPath encodedVideoPath originalFileName; do
    # Clean paths: remove quotes, carriage returns, and trim whitespace
    src=$(echo "$encodedVideoPath" | tr -d '\r' | tr -d '"' | xargs)
    dst=$(echo "$originalPath" | tr -d '\r' | tr -d '"' | xargs)
    filename=$(echo "$originalFileName" | tr -d '\r' | tr -d '"' | xargs)
    file_id=$(echo "$id" | tr -d '\r' | tr -d '"' | xargs)

    if [[ -z "$src" || -z "$dst" ]]; then
        echo "⚠️ Skipping row with missing paths (ID: $file_id)"
        continue
    fi

    echo ""
    echo "🔍 Checking: '$src' (Original: $filename)"

    if [[ ! -f "$src" ]]; then
        echo "❌ Missing: $src"
        continue
    fi

    # Check if the video is valid (not corrupted)
    ffmpeg -v quiet -threads "$(nproc)" -i "$src" -f null - </dev/null >/dev/null 2>&1
    TEST_RESULT=$?

    if [[ "$TEST_RESULT" != 0 ]]; then
        echo "❌ Corrupted: $src"
        continue
    fi

    echo "✅ Valid: $src"

    if [[ "$COPY" == true ]]; then
        mkdir -p "$(dirname "$dst")"
        cp "$src" "$dst"
        echo "📁 Copied → $dst"
    else
        echo "📁 [Would copy \"$src\" → \"$dst\"]"
    fi
done