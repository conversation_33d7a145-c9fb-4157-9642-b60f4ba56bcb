#!/bin/bash

CSV_FILE="data-data-1750674776329.csv"
COPY=false
BACKUP_DIR="/media/Data/@mydata/Images/backup_videos/"

[[ "$*" == *--copy* ]] && COPY=true
[[ "$COPY" == false ]] && echo "ℹ️ Dry run mode (use --copy to actually copy files)"

# Function to check if file exists and is valid
check_file() {
    local file="$1"
    local type="$2"
    
    [[ -z "$file" ]] && { echo "  ⚠️ No $type path provided"; return 1; }
    echo "  📂 Checking $type: '$file'"
    
    [[ ! -f "$file" ]] && { echo "  ❌ $type file missing"; return 1; }
    echo "  ✅ $type file exists"
    
    ffmpeg -v quiet -threads "$(nproc)" -i "$file" -f null - </dev/null >/dev/null 2>&1
    [[ $? != 0 ]] && { echo "  ❌ $type file is corrupted"; return 1; }
    
    echo "  ✅ $type file is valid"
    return 0
}

tail -n +2 "$CSV_FILE" | while IFS=',' read -r id originalPath encodedVideoPath originalFileName; do
    # Clean paths
    original=$(echo "$originalPath" | tr -d '\r"' | xargs)
    encoded=$(echo "$encodedVideoPath" | tr -d '\r"' | xargs)
    filename=$(echo "$originalFileName" | tr -d '\r"' | xargs)
    file_id=$(echo "$id" | tr -d '\r"' | xargs)

    echo ""
    echo "🔍 Processing: $filename (ID: $file_id)"

    # Check original first, then encoded
    if check_file "$original" "original"; then
        source_file="$original"
        source_type="original"
    elif check_file "$encoded" "encoded"; then
        source_file="$encoded"
        source_type="encoded"
    else
        echo "  ❌ No valid file found for $filename"
        continue
    fi

    # Copy logic
    dst="$BACKUP_DIR$filename"
    if [[ "$COPY" == true ]]; then
        mkdir -p "$BACKUP_DIR"
        cp "$source_file" "$dst"
        echo "  📁 Copied ($source_type) → $dst"
    else
        echo "  📁 [Would copy ($source_type) \"$source_file\" → \"$dst\"]"
    fi
done