#!/usr/bin/env python3
"""
Script to verify original files and preview files from CSV data.
For each record in the CSV:
1. Verify the originalPath file exists and can be identified
2. Check if the corresponding preview file exists and can be identified

Preview path format: /media/Data/@mydata/Images/thumbs/e9038fc0-e1ac-48f6-84ac-17838f91b108/{xx}/{yy}/{id}-preview.jpeg
where xx = first 2 chars of id, yy = chars 3-4 of id
"""

import csv
import os
import subprocess
import sys
from pathlib import Path

def run_identify(file_path):
    """Run ImageMagick identify command on a file to verify it's a valid image."""
    try:
        result = subprocess.run(['identify', file_path], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Timeout"
    except FileNotFoundError:
        return False, "", "identify command not found"
    except Exception as e:
        return False, "", str(e)

def build_preview_path(file_id):
    """Build the preview file path from the ID."""
    if len(file_id) < 4:
        return None
    
    xx = file_id[:2]
    yy = file_id[2:4]
    preview_path = f"/media/Data/@mydata/Images/thumbs/e9038fc0-e1ac-48f6-84ac-17838f91b108/{xx}/{yy}/{file_id}-preview.jpeg"
    return preview_path

def verify_file(file_path, file_type="file"):
    """Verify if a file exists and is a valid image."""
    if not os.path.exists(file_path):
        return {
            'exists': False,
            'identify_success': False,
            'identify_output': '',
            'error': 'File not found'
        }
    
    # Check if it's a file (not directory)
    if not os.path.isfile(file_path):
        return {
            'exists': True,
            'identify_success': False,
            'identify_output': '',
            'error': 'Path exists but is not a file'
        }
    
    # Run identify command
    success, output, error = run_identify(file_path)
    
    return {
        'exists': True,
        'identify_success': success,
        'identify_output': output,
        'error': error if not success else ''
    }

def main():
    csv_file = 'immich/data-1750703402192.csv'
    
    if not os.path.exists(csv_file):
        print(f"Error: CSV file '{csv_file}' not found")
        sys.exit(1)
    
    # Counters for statistics
    total_records = 0
    original_missing = 0
    original_invalid = 0
    preview_missing = 0
    preview_invalid = 0
    
    # Lists to store problematic files
    missing_originals = []
    invalid_originals = []
    missing_previews = []
    invalid_previews = []
    
    print("Starting file verification...")
    print("=" * 80)
    
    with open(csv_file, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row_num, row in enumerate(reader, 1):
            total_records += 1
            file_id = row['id']
            original_path = row['originalPath']
            
            # Progress indicator
            if total_records % 100 == 0:
                print(f"Processed {total_records} records...")
            
            # Verify original file
            original_result = verify_file(original_path, "original")
            
            if not original_result['exists']:
                original_missing += 1
                missing_originals.append({
                    'id': file_id,
                    'path': original_path,
                    'error': original_result['error']
                })
            elif not original_result['identify_success']:
                original_invalid += 1
                invalid_originals.append({
                    'id': file_id,
                    'path': original_path,
                    'error': original_result['error']
                })
            
            # Build and verify preview file
            preview_path = build_preview_path(file_id)
            if preview_path:
                preview_result = verify_file(preview_path, "preview")
                
                if not preview_result['exists']:
                    preview_missing += 1
                    missing_previews.append({
                        'id': file_id,
                        'path': preview_path,
                        'error': preview_result['error']
                    })
                elif not preview_result['identify_success']:
                    preview_invalid += 1
                    invalid_previews.append({
                        'id': file_id,
                        'path': preview_path,
                        'error': preview_result['error']
                    })
    
    # Print summary
    print("\n" + "=" * 80)
    print("VERIFICATION SUMMARY")
    print("=" * 80)
    print(f"Total records processed: {total_records}")
    print()
    print("ORIGINAL FILES:")
    print(f"  Missing: {original_missing}")
    print(f"  Invalid (failed identify): {original_invalid}")
    print(f"  Valid: {total_records - original_missing - original_invalid}")
    print()
    print("PREVIEW FILES:")
    print(f"  Missing: {preview_missing}")
    print(f"  Invalid (failed identify): {preview_invalid}")
    print(f"  Valid: {total_records - preview_missing - preview_invalid}")
    
    # Print detailed lists if there are issues
    if missing_originals:
        print("\n" + "=" * 80)
        print("MISSING ORIGINAL FILES:")
        print("=" * 80)
        for item in missing_originals[:20]:  # Show first 20
            print(f"ID: {item['id']}")
            print(f"Path: {item['path']}")
            print(f"Error: {item['error']}")
            print("-" * 40)
        if len(missing_originals) > 20:
            print(f"... and {len(missing_originals) - 20} more")
    
    if invalid_originals:
        print("\n" + "=" * 80)
        print("INVALID ORIGINAL FILES (failed identify):")
        print("=" * 80)
        for item in invalid_originals[:20]:  # Show first 20
            print(f"ID: {item['id']}")
            print(f"Path: {item['path']}")
            print(f"Error: {item['error']}")
            print("-" * 40)
        if len(invalid_originals) > 20:
            print(f"... and {len(invalid_originals) - 20} more")
    
    if missing_previews:
        print("\n" + "=" * 80)
        print("MISSING PREVIEW FILES:")
        print("=" * 80)
        for item in missing_previews[:20]:  # Show first 20
            print(f"ID: {item['id']}")
            print(f"Path: {item['path']}")
            print(f"Error: {item['error']}")
            print("-" * 40)
        if len(missing_previews) > 20:
            print(f"... and {len(missing_previews) - 20} more")
    
    if invalid_previews:
        print("\n" + "=" * 80)
        print("INVALID PREVIEW FILES (failed identify):")
        print("=" * 80)
        for item in invalid_previews[:20]:  # Show first 20
            print(f"ID: {item['id']}")
            print(f"Path: {item['path']}")
            print(f"Error: {item['error']}")
            print("-" * 40)
        if len(invalid_previews) > 20:
            print(f"... and {len(invalid_previews) - 20} more")
    
    # Save detailed reports to files
    if missing_originals:
        with open('missing_originals.txt', 'w') as f:
            f.write("Missing Original Files Report\n")
            f.write("=" * 50 + "\n\n")
            for item in missing_originals:
                f.write(f"ID: {item['id']}\n")
                f.write(f"Path: {item['path']}\n")
                f.write(f"Error: {item['error']}\n")
                f.write("-" * 40 + "\n")
        print(f"\nDetailed report saved to: missing_originals.txt")
    
    if invalid_originals:
        with open('invalid_originals.txt', 'w') as f:
            f.write("Invalid Original Files Report\n")
            f.write("=" * 50 + "\n\n")
            for item in invalid_originals:
                f.write(f"ID: {item['id']}\n")
                f.write(f"Path: {item['path']}\n")
                f.write(f"Error: {item['error']}\n")
                f.write("-" * 40 + "\n")
        print(f"Detailed report saved to: invalid_originals.txt")
    
    if missing_previews:
        with open('missing_previews.txt', 'w') as f:
            f.write("Missing Preview Files Report\n")
            f.write("=" * 50 + "\n\n")
            for item in missing_previews:
                f.write(f"ID: {item['id']}\n")
                f.write(f"Path: {item['path']}\n")
                f.write(f"Error: {item['error']}\n")
                f.write("-" * 40 + "\n")
        print(f"Detailed report saved to: missing_previews.txt")
    
    if invalid_previews:
        with open('invalid_previews.txt', 'w') as f:
            f.write("Invalid Preview Files Report\n")
            f.write("=" * 50 + "\n\n")
            for item in invalid_previews:
                f.write(f"ID: {item['id']}\n")
                f.write(f"Path: {item['path']}\n")
                f.write(f"Error: {item['error']}\n")
                f.write("-" * 40 + "\n")
        print(f"Detailed report saved to: invalid_previews.txt")

if __name__ == "__main__":
    main()
