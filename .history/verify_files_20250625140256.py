#!/usr/bin/env python3
"""
<PERSON><PERSON>t to verify and organize image files from CSV data.
For each record in the CSV:
1. Verify the originalPath file exists and can be identified
2. For missing/corrupted originals: check preview files
3. Copy files to appropriate destinations based on status

Workflow:
- Verified originals -> /media/Data/@mydata/Images/quynhttn_reupload/
- Corrupted originals -> /media/Data/@archived/quynhttn_images/
- Verified previews (for missing/corrupted originals) -> /media/Data/@mydata/Images/quynhttn_reupload/preview/
"""

import csv
import os
import subprocess
import sys
import shutil

def run_identify(file_path):
    """Run ImageMagick identify command on a file to verify it's a valid image."""
    try:
        result = subprocess.run(['identify', file_path],
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Timeout"
    except FileNotFoundError:
        return False, "", "identify command not found"
    except Exception as e:
        return False, "", str(e)

def build_preview_path(file_id):
    """Build the preview file path from the ID."""
    if len(file_id) < 4:
        return None

    xx = file_id[:2]
    yy = file_id[2:4]
    preview_path = f"/media/Data/@mydata/Images/thumbs/e9038fc0-e1ac-48f6-84ac-17838f91b108/{xx}/{yy}/{file_id}-preview.jpeg"
    return preview_path

def verify_file(file_path):
    """Verify if a file exists and is a valid image."""
    if not os.path.exists(file_path):
        return False, "File not found"

    if not os.path.isfile(file_path):
        return False, "Path exists but is not a file"

    # Run identify command
    success, _, error = run_identify(file_path)
    return success, error if not success else "OK"

def extract_date_path(original_path):
    """Extract the date path (e.g., '2025/2025-05-22/') from original path."""
    try:
        # Find the pattern like /2025/2025-05-22/ in the path
        parts = original_path.split('/')
        for i, part in enumerate(parts):
            if part.startswith('20') and len(part) == 4:  # Year like 2025
                if i + 1 < len(parts) and parts[i + 1].startswith('20'):  # Date like 2025-05-22
                    return f"{part}/{parts[i + 1]}/"
        return "unknown/"
    except:
        return "unknown/"

def safe_copy_file(src_path, dest_path):
    """Safely copy a file, creating directories as needed."""
    try:
        # Create destination directory if it doesn't exist
        dest_dir = os.path.dirname(dest_path)
        os.makedirs(dest_dir, exist_ok=True)

        # Copy the file
        shutil.copy2(src_path, dest_path)
        return True, "Copied successfully"
    except Exception as e:
        return False, str(e)

def main():
    csv_file = 'immich/data-1750703402192.csv'

    if not os.path.exists(csv_file):
        print(f"Error: CSV file '{csv_file}' not found")
        sys.exit(1)

    # Base destination paths
    reupload_base = "/media/Data/@mydata/Images/quynhttn_reupload/"
    archived_base = "/media/Data/@archived/quynhttn_images/"
    preview_base = "/media/Data/@mydata/Images/quynhttn_reupload/preview/"

    # Counters for statistics
    total_records = 0
    verified_originals = 0
    corrupted_originals = 0
    missing_originals = 0
    verified_previews = 0
    missing_previews = 0

    # Lists for reporting
    all_results = []

    print("Starting file verification and organization...")
    print("=" * 80)

    with open(csv_file, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)

        for row_num, row in enumerate(reader, 1):
            total_records += 1
            file_id = row['id']
            original_path = row['originalPath']

            # Progress indicator
            if total_records % 100 == 0:
                print(f"Processed {total_records} records...")

            # Extract date path for organizing files
            date_path = extract_date_path(original_path)
            filename = os.path.basename(original_path)

            # Verify original file
            original_valid, original_error = verify_file(original_path)

            result = {
                'id': file_id,
                'original_path': original_path,
                'filename': filename,
                'date_path': date_path,
                'status': '',
                'actions': [],
                'errors': []
            }

            if not os.path.exists(original_path):
                # Original file is missing
                missing_originals += 1
                result['status'] = 'MISSING_ORIGINAL'
                result['errors'].append(f"Original file not found: {original_path}")

                # Check preview file for missing original
                preview_path = build_preview_path(file_id)
                if preview_path:
                    preview_valid, preview_error = verify_file(preview_path)
                    if preview_valid:
                        verified_previews += 1
                        # Copy preview to reupload/preview folder
                        dest_preview = os.path.join(preview_base, date_path, f"{file_id}-preview.jpeg")
                        copy_success, copy_error = safe_copy_file(preview_path, dest_preview)
                        if copy_success:
                            result['actions'].append(f"Copied preview to: {dest_preview}")
                        else:
                            result['errors'].append(f"Failed to copy preview: {copy_error}")
                    else:
                        missing_previews += 1
                        result['errors'].append(f"Preview also missing/invalid: {preview_error}")

            elif original_valid:
                # Original file is verified - copy to reupload folder
                verified_originals += 1
                result['status'] = 'VERIFIED_ORIGINAL'
                dest_original = os.path.join(reupload_base, date_path, filename)
                copy_success, copy_error = safe_copy_file(original_path, dest_original)
                if copy_success:
                    result['actions'].append(f"Copied original to: {dest_original}")
                else:
                    result['errors'].append(f"Failed to copy original: {copy_error}")

            else:
                # Original file is corrupted
                corrupted_originals += 1
                result['status'] = 'CORRUPTED_ORIGINAL'
                result['errors'].append(f"Original file corrupted: {original_error}")

                # Copy corrupted original to archived folder
                dest_archived = os.path.join(archived_base, date_path, filename)
                copy_success, copy_error = safe_copy_file(original_path, dest_archived)
                if copy_success:
                    result['actions'].append(f"Copied corrupted original to: {dest_archived}")
                else:
                    result['errors'].append(f"Failed to copy corrupted original: {copy_error}")

                # Check preview file for corrupted original
                preview_path = build_preview_path(file_id)
                if preview_path:
                    preview_valid, preview_error = verify_file(preview_path)
                    if preview_valid:
                        verified_previews += 1
                        # Copy preview to reupload/preview folder
                        dest_preview = os.path.join(preview_base, date_path, f"{file_id}-preview.jpeg")
                        copy_success, copy_error = safe_copy_file(preview_path, dest_preview)
                        if copy_success:
                            result['actions'].append(f"Copied preview to: {dest_preview}")
                        else:
                            result['errors'].append(f"Failed to copy preview: {copy_error}")
                    else:
                        missing_previews += 1
                        result['errors'].append(f"Preview also missing/invalid: {preview_error}")

            all_results.append(result)

    # Print summary
    print("\n" + "=" * 80)
    print("PROCESSING SUMMARY")
    print("=" * 80)
    print(f"Total records processed: {total_records}")
    print(f"Verified originals: {verified_originals}")
    print(f"Corrupted originals: {corrupted_originals}")
    print(f"Missing originals: {missing_originals}")
    print(f"Verified previews (for missing/corrupted): {verified_previews}")
    print(f"Missing previews: {missing_previews}")

    # Generate comprehensive report
    with open('processing_report.txt', 'w') as f:
        f.write("Image Processing Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total records processed: {total_records}\n")
        f.write(f"Verified originals: {verified_originals}\n")
        f.write(f"Corrupted originals: {corrupted_originals}\n")
        f.write(f"Missing originals: {missing_originals}\n")
        f.write(f"Verified previews: {verified_previews}\n")
        f.write(f"Missing previews: {missing_previews}\n\n")

        for result in all_results:
            f.write(f"ID: {result['id']}\n")
            f.write(f"Status: {result['status']}\n")
            f.write(f"Original: {result['original_path']}\n")

            if result['actions']:
                f.write("Actions:\n")
                for action in result['actions']:
                    f.write(f"  - {action}\n")

            if result['errors']:
                f.write("Errors:\n")
                for error in result['errors']:
                    f.write(f"  - {error}\n")

            f.write("-" * 40 + "\n")

    print(f"\nDetailed report saved to: processing_report.txt")

if __name__ == "__main__":
    main()
