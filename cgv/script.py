import requests
import schedule
import asyncio
from datetime import datetime
import telegram
import os
import logging
from dotenv import load_dotenv

# Parameters (change these as needed)
CINEMA_ID = '25002400'  # Default cinema ID
DATE = '20250314'       # Default date (YYYYMMDD)

# Load environment variables
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# Initialize Telegram bot
bot = telegram.Bot(token=TELEGRAM_BOT_TOKEN)

# Set up logging with more detail
logging.basicConfig(
    filename='/home/<USER>/scripts/cgv/cgv_checker.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def check_schedule():
    url = 'https://www.cgv.vn/default/cinemas/product/ajaxschedule/'
    headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
    }
    data = {'id': CINEMA_ID, 'dy': DATE}

    try:
        response = requests.post(url, headers=headers, data=data)
        response.raise_for_status()
        error_message = "Xin lỗi, không có suất chiếu vào ngày này, hãy chọn một ngày khác"
        HN = "Hà Nội"
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Detailed message for every result
        if error_message not in response.text and HN in response.text:
            status = "AVAILABLE"
            log_msg = (
                f"Check completed\n"
                f"Time: {current_time}\n"
                f"Cinema ID: {data['id']}\n"
                f"Date: {data['dy']}\n"
                f"Status: Showtimes AVAILABLE\n"
                f"Response (full): {response.text}"
            )
            telegram_msg = (
                f"✅ Showtimes Check\n"
                f"Time: {current_time}\n"
                f"Cinema ID: {data['id']}\n"
                f"Date: {data['dy']}\n"
                f"Status: Showtimes AVAILABLE\n"
                f"Response (first 200 chars): {response.text[:200]}..."
            )
        else:
            status = "NOT AVAILABLE"
            log_msg = (
                f"Check completed\n"
                f"Time: {current_time}\n"
                f"Cinema ID: {data['id']}\n"
                f"Date: {data['dy']}\n"
                f"Status: Showtimes NOT AVAILABLE\n"
                f"Response (full): {response.text}"
            )
            telegram_msg = (
                f"❌ Showtimes Check\n"
                f"Time: {current_time}\n"
                f"Cinema ID: {data['id']}\n"
                f"Date: {data['dy']}\n"
                f"Status: Showtimes NOT AVAILABLE\n"
                f"Response (first 200 chars): {response.text[:200]}..."
            )

        # Log detailed result
        logging.info(log_msg)
        # Send Telegram message for every check
        await bot.send_message(chat_id=TELEGRAM_CHAT_ID, text=telegram_msg)
        print(f"Check complete: {status}")

    except Exception as e:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        error_log_msg = (
            f"Check failed\n"
            f"Time: {current_time}\n"
            f"Cinema ID: {data['id']}\n"
            f"Date: {data['dy']}\n"
            f"Error: {str(e)}"
        )
        error_telegram_msg = f"⚠️ Error\nTime: {current_time}\nError: {str(e)}"
        logging.error(error_log_msg)
        await bot.send_message(chat_id=TELEGRAM_CHAT_ID, text=error_telegram_msg)
        print(f"Error: {str(e)}")

async def main():
    # Send startup message
    startup_msg = (
        f"🚀 Script Started\n"
        f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        f"Checking Cinema ID: {CINEMA_ID}\n"
        f"Date: {DATE}"
    )
    await bot.send_message(chat_id=TELEGRAM_CHAT_ID, text=startup_msg)
    logging.info(
        f"Script started\n"
        f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        f"Cinema ID: {CINEMA_ID}\n"
        f"Date: {DATE}"
    )
    print("Startup message sent")

    # Run an immediate check at startup
    await check_schedule()
    print("Initial check completed")

    # Schedule recurring jobs
    schedule.every().hour.at(":10").do(lambda: asyncio.create_task(check_schedule()))
    schedule.every(5).to(10).minutes.do(lambda: asyncio.create_task(check_schedule()))

    # Main loop
    while True:
        schedule.run_pending()
        await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(main())