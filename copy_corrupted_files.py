#!/usr/bin/env python3
"""
Script to copy corrupted video files to archive folder.

This script:
1. Extracts IDs from scan_csv_quynh_rs.txt that have "❌ original file is corrupted"
2. Looks up the originalPath for each ID in data-1750674776329.csv
3. Copies the files to /media/Data/@archived/corrupted/quynhttn_videos/
"""

import re
import csv
import os
import shutil
import argparse
from pathlib import Path
from typing import Set, Dict, List


def extract_corrupted_ids(scan_file: str) -> Set[str]:
    """Extract IDs from scan file that have corrupted original files."""
    corrupted_ids = set()
    
    with open(scan_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    current_id = None
    for line in lines:
        # Look for processing lines with ID
        processing_match = re.search(r'🔍 Processing: .+ \(ID: ([a-f0-9-]+)\)', line)
        if processing_match:
            current_id = processing_match.group(1)
        
        # Check if the next line indicates corruption
        elif current_id and "❌ original file is corrupted" in line:
            corrupted_ids.add(current_id)
            current_id = None
        
        # Reset if we see a new processing line
        elif "🔍 Processing:" in line:
            current_id = None
    
    return corrupted_ids


def load_csv_data(csv_file: str) -> Dict[str, str]:
    """Load CSV data and return mapping of ID to originalPath."""
    id_to_path = {}
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            id_to_path[row['id']] = row['originalPath']
    
    return id_to_path


def copy_files(corrupted_ids: Set[str], id_to_path: Dict[str, str], 
               destination_dir: str, dry_run: bool = True) -> None:
    """Copy corrupted files to destination directory."""
    
    # Create destination directory if it doesn't exist
    dest_path = Path(destination_dir)
    if not dry_run:
        dest_path.mkdir(parents=True, exist_ok=True)
    
    copied_count = 0
    missing_count = 0
    not_found_in_csv = 0
    
    print(f"{'🔍 DRY RUN MODE' if dry_run else '📁 COPY MODE'}")
    print(f"Found {len(corrupted_ids)} corrupted file IDs")
    print(f"Destination: {destination_dir}")
    print("-" * 80)
    
    for file_id in sorted(corrupted_ids):
        if file_id not in id_to_path:
            print(f"❌ ID not found in CSV: {file_id}")
            not_found_in_csv += 1
            continue
        
        original_path = id_to_path[file_id]
        if not original_path:
            print(f"❌ Empty originalPath for ID: {file_id}")
            missing_count += 1
            continue
        
        source_file = Path(original_path)
        if not source_file.exists():
            print(f"❌ Source file not found: {original_path}")
            missing_count += 1
            continue
        
        # Create destination filename
        dest_file = dest_path / source_file.name
        
        # Handle filename conflicts by adding a suffix
        counter = 1
        while dest_file.exists():
            stem = source_file.stem
            suffix = source_file.suffix
            dest_file = dest_path / f"{stem}_{counter}{suffix}"
            counter += 1
        
        if dry_run:
            print(f"📋 Would copy: {source_file.name}")
            print(f"   From: {original_path}")
            print(f"   To:   {dest_file}")
        else:
            try:
                shutil.copy2(original_path, dest_file)
                print(f"✅ Copied: {source_file.name}")
                print(f"   From: {original_path}")
                print(f"   To:   {dest_file}")
            except Exception as e:
                print(f"❌ Failed to copy {source_file.name}: {e}")
                continue
        
        copied_count += 1
        print()
    
    print("-" * 80)
    print(f"Summary:")
    print(f"  {'Would copy' if dry_run else 'Copied'}: {copied_count} files")
    print(f"  Missing/not found: {missing_count} files")
    print(f"  Not found in CSV: {not_found_in_csv} files")
    print(f"  Total corrupted IDs: {len(corrupted_ids)}")


def main():
    parser = argparse.ArgumentParser(description='Copy corrupted video files to archive folder')
    parser.add_argument('--scan-file', default='immich/scan_csv_quynh_rs.txt',
                       help='Path to scan results file')
    parser.add_argument('--csv-file', default='immich/data-1750674776329.csv',
                       help='Path to CSV data file')
    parser.add_argument('--destination', default='/media/Data/@archived/corrupted/quynhttn_videos/',
                       help='Destination directory for corrupted files')
    parser.add_argument('--copy', action='store_true',
                       help='Actually copy files (default is dry run)')
    
    args = parser.parse_args()
    
    # Check if input files exist
    if not os.path.exists(args.scan_file):
        print(f"❌ Scan file not found: {args.scan_file}")
        return 1
    
    if not os.path.exists(args.csv_file):
        print(f"❌ CSV file not found: {args.csv_file}")
        return 1
    
    print("🔍 Extracting corrupted file IDs...")
    corrupted_ids = extract_corrupted_ids(args.scan_file)
    
    print("📊 Loading CSV data...")
    id_to_path = load_csv_data(args.csv_file)
    
    print("📁 Processing files...")
    copy_files(corrupted_ids, id_to_path, args.destination, dry_run=not args.copy)
    
    return 0


if __name__ == '__main__':
    exit(main())
