#!/usr/bin/env python3
"""
Script to copy preview files to replace missing original files.
- Copies preview files when original is missing but preview exists
- Sets file date to match the date in the folder path
- Lists IDs of files missing both original and preview
"""

import csv
import os
import sys
import shutil
import re
from datetime import datetime
from pathlib import Path

def get_preview_path(file_id):
    """Generate preview path based on ID pattern."""
    if len(file_id) < 4:
        return None
    
    base_uuid = "9c08a44f-3a09-4dce-a446-5a700f1b2e9e"
    first_four = file_id[:4]
    xx = first_four[:2]
    yy = first_four[2:4]
    preview_filename = f"{file_id}-preview.jpeg"
    preview_path = f"/media/Data/@mydata/Images/thumbs/{base_uuid}/{xx}/{yy}/{preview_filename}"
    return preview_path

def extract_date_from_path(original_path):
    """Extract date from folder path like /path/2014/2014-08-26/file.jpg"""
    # Look for pattern YYYY/YYYY-MM-DD in the path
    pattern = r'/(\d{4})/(\d{4}-\d{2}-\d{2})/'
    match = re.search(pattern, original_path)
    if match:
        year = match.group(1)
        date_str = match.group(2)
        try:
            # Parse the date
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj
        except ValueError:
            print(f"  ⚠ Could not parse date from path: {original_path}")
            return None
    else:
        print(f"  ⚠ No date pattern found in path: {original_path}")
        return None

def set_file_date(file_path, target_date):
    """Set file modification and access time to target date."""
    try:
        # Convert datetime to timestamp
        timestamp = target_date.timestamp()
        # Set both access time and modification time
        os.utime(file_path, (timestamp, timestamp))
        return True
    except Exception as e:
        print(f"  ⚠ Could not set date for {file_path}: {e}")
        return False

def copy_preview_to_original(csv_file_path, dry_run=True):
    """
    Copy preview files to replace missing originals and set dates.
    
    Args:
        csv_file_path: Path to the CSV file
        dry_run: If True, only show what would be done without actually copying
    """
    
    if not os.path.exists(csv_file_path):
        print(f"ERROR: CSV file not found: {csv_file_path}")
        return
    
    print(f"Processing CSV file: {csv_file_path}")
    if dry_run:
        print("DRY RUN MODE: Will show what would be done without actually copying files")
    print("=" * 80)
    
    copied_files = 0
    missing_both = []
    total_missing_originals = 0
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            for row_num, row in enumerate(reader, start=2):
                file_id = row.get('id', '').strip('"')
                file_type = row.get('type', '').strip('"')
                original_path = row.get('originalPath', '').strip('"')
                original_filename = row.get('originalFileName', '').strip('"')
                
                # Only process IMAGE files
                if file_type.upper() != 'IMAGE':
                    continue
                
                # Check if original file exists
                if not original_path or os.path.exists(original_path):
                    continue
                
                total_missing_originals += 1
                print(f"\nRow {row_num}: ID={file_id}")
                print(f"  Missing original: {original_path}")
                
                # Check if preview file exists
                preview_path = get_preview_path(file_id)
                if not preview_path or not os.path.exists(preview_path):
                    print(f"  ✗ Preview also missing: {preview_path}")
                    missing_both.append(file_id)
                    continue
                
                print(f"  ✓ Preview found: {preview_path}")
                
                # Extract date from original path
                target_date = extract_date_from_path(original_path)
                if not target_date:
                    print(f"  ⚠ Skipping due to date extraction failure")
                    continue
                
                print(f"  📅 Target date: {target_date.strftime('%Y-%m-%d')}")
                
                if dry_run:
                    print(f"  [DRY RUN] Would copy: {preview_path}")
                    print(f"  [DRY RUN] Would copy to: {original_path}")
                    print(f"  [DRY RUN] Would set date to: {target_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    copied_files += 1
                else:
                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(original_path), exist_ok=True)
                    
                    try:
                        # Copy the preview file to original location
                        shutil.copy2(preview_path, original_path)
                        print(f"  ✓ Copied: {preview_path} -> {original_path}")
                        
                        # Set the file date
                        if set_file_date(original_path, target_date):
                            print(f"  ✓ Set date to: {target_date.strftime('%Y-%m-%d %H:%M:%S')}")
                        
                        copied_files += 1
                        
                    except Exception as e:
                        print(f"  ✗ Copy failed: {e}")
                
                print("-" * 60)
    
    except Exception as e:
        print(f"ERROR reading CSV file: {e}")
        return
    
    # Write missing both IDs to file
    missing_both_file = "missing_both_original_and_preview.txt"
    try:
        with open(missing_both_file, 'w') as f:
            for file_id in missing_both:
                f.write(f"{file_id}\n")
        print(f"\n📝 Written {len(missing_both)} IDs missing both files to: {missing_both_file}")
    except Exception as e:
        print(f"ERROR writing missing IDs file: {e}")
    
    # Print summary
    print("\n" + "=" * 80)
    print("SUMMARY REPORT")
    print("=" * 80)
    print(f"Total missing original files (images): {total_missing_originals}")
    print(f"Files copied from preview: {copied_files}")
    print(f"Files missing both original and preview: {len(missing_both)}")
    
    if dry_run:
        print("\nTo actually perform the copy operations, run without --dry-run flag")
    
    print(f"\nMissing both IDs saved to: {missing_both_file}")

def main():
    """Main function."""
    
    # Default CSV file path
    default_csv = "immich/data-1750883770936.csv"
    
    # Check for dry run flag
    dry_run = "--dry-run" in sys.argv or "-n" in sys.argv
    
    # Filter out flags to get the CSV file argument
    args = [arg for arg in sys.argv[1:] if not arg.startswith('-')]
    
    # Check command line arguments
    if len(args) > 0:
        csv_file = args[0]
    else:
        csv_file = default_csv
    
    if dry_run:
        print("DRY RUN MODE: Will simulate operations without actually copying files")
        print("Use without --dry-run or -n flag to perform actual operations")
        print()
    
    copy_preview_to_original(csv_file, dry_run=dry_run)

if __name__ == "__main__":
    main()
