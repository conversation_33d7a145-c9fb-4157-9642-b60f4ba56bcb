#!/usr/bin/env python3
"""
Script to extract IDs from CSV file to a text file.
"""

import csv
import sys
import os

def extract_ids_from_csv(csv_file_path, output_file_path):
    """
    Extract IDs from the first column of a CSV file and write them to a text file.
    
    Args:
        csv_file_path (str): Path to the input CSV file
        output_file_path (str): Path to the output text file
    """
    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            
            # Skip the header row
            header = next(reader)
            print(f"Header: {header}")
            
            ids = []
            for row_num, row in enumerate(reader, start=2):  # Start at 2 since we skipped header
                if row:  # Skip empty rows
                    id_value = row[0].strip('"')  # Remove quotes if present
                    ids.append(id_value)
            
            print(f"Extracted {len(ids)} IDs from CSV file")
            
            # Write IDs to text file
            with open(output_file_path, 'w', encoding='utf-8') as txtfile:
                for id_value in ids:
                    txtfile.write(f"{id_value}\n")
            
            print(f"IDs written to: {output_file_path}")
            return len(ids)
            
    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file_path}' not found")
        return 0
    except Exception as e:
        print(f"Error processing CSV file: {e}")
        return 0

def main():
    # Default file paths
    csv_file = "immich/data-1750703402192.csv"
    output_file = "extracted_ids.txt"
    
    # Check if CSV file exists
    if not os.path.exists(csv_file):
        print(f"Error: CSV file '{csv_file}' not found")
        sys.exit(1)
    
    print(f"Extracting IDs from: {csv_file}")
    print(f"Output file: {output_file}")
    
    count = extract_ids_from_csv(csv_file, output_file)
    
    if count > 0:
        print(f"\nSuccess! Extracted {count} IDs to '{output_file}'")
    else:
        print("Failed to extract IDs")
        sys.exit(1)

if __name__ == "__main__":
    main()
