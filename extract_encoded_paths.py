#!/usr/bin/env python3
"""
Script to extract encoded video file paths from the scan log file.
Looks for lines with "✅ encoded file is valid" and extracts the encoded file path
from the following line that contains "[Would copy (encoded)".
"""

import re

def extract_encoded_paths(input_file, output_file):
    """Extract encoded video paths from the log file."""
    encoded_paths = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for lines with "✅ encoded file is valid"
        if "✅ encoded file is valid" in line:
            # Check the next line for the encoded path
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                
                # Look for pattern: [Would copy (encoded) "path" → "destination"]
                match = re.search(r'\[Would copy \(encoded\) "([^"]+)"', next_line)
                if match:
                    encoded_path = match.group(1)
                    encoded_paths.append(encoded_path)
                    print(f"Found encoded path: {encoded_path}")
        
        i += 1
    
    # Write the paths to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        for path in encoded_paths:
            f.write(path + '\n')
    
    print(f"\nExtracted {len(encoded_paths)} encoded video paths to {output_file}")

if __name__ == "__main__":
    input_file = "immich/scan_csv_quynh_rs.txt"
    output_file = "move_videos.txt"
    
    extract_encoded_paths(input_file, output_file)
