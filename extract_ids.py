#!/usr/bin/env python3

import re

def extract_invalid_video_ids(input_file, output_file):
    """Extract IDs from entries that have '❌ No valid file found for' message"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into blocks starting with "🔍 Processing:"
    blocks = re.split(r'(?=🔍 Processing:)', content)
    
    invalid_ids = []
    
    for block in blocks:
        if not block.strip():
            continue
            
        # Check if this block contains "❌ No valid file found for"
        if "❌ No valid file found for" in block:
            # Extract the ID from the first line
            id_match = re.search(r'ID: ([^)]+)', block)
            if id_match:
                invalid_ids.append(id_match.group(1))
    
    # Write IDs to output file, one per line
    with open(output_file, 'w') as f:
        for id_val in invalid_ids:
            f.write(f"{id_val}\n")
    
    print(f"Extracted {len(invalid_ids)} IDs to {output_file}")

if __name__ == "__main__":
    extract_invalid_video_ids("immich/scan_csv_quynh_rs.txt", "remove_video.txt")
