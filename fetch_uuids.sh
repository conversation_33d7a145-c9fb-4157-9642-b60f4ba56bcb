#!/bin/bash

API_KEY="aS8tBo6eaWO4NKpBN049e1cbKfH3cG7H71w4Hggxo"
DEVICE_ID="4c01ab7b3c57786c80cd6a20dc676869ed01025279e4945b6dc2bc4412af022d"
OUTPUT_FILE="uuids.txt"
BASE_URL="https://image.haivita.top/api/search/metadata"

echo "Clearing previous UUID file..."
> "$OUTPUT_FILE"

for PAGE in {1..5}; do
  echo "Fetching page $PAGE..."
  RESPONSE=$(curl --silent --location --request POST "$BASE_URL" \
    --header "x-api-key: $API_KEY" \
    --header "Content-Type: application/json" \
    --data-raw "{
      \"size\": 1000,
      \"page\": $PAGE,
      \"deviceId\": \"$DEVICE_ID\"
    }")

  # Extract UUIDs (asset ids) and write to file
  echo "$RESPONSE" | jq -r '.assets.items[]?.id' >> "$OUTPUT_FILE"
done

echo "Done. UUIDs written to $OUTPUT_FILE"
