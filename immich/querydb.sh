#!/bin/bash

QUERY="
SELECT
  id,
  \"originalFileName\",
  TO_CHAR(TO_DATE(substring(\"originalFileName\" FROM '[0-9]{8}'), 'YYYYMMDD'), 'YYYY-MM-DD') AS image_date
FROM assets
WHERE \"originalFileName\" = 'IMG_20231123_110840.jpg';
"

# Define your container and database credentials
DB_CONTAINER="immich_postgres"  # change this to your actual container name
DB_NAME="immich"
DB_USER="haivita"
DB_PASSWORD="haivita181207"

# Run the query inside the Docker container
docker exec -e PGPASSWORD=$DB_PASSWORD $DB_CONTAINER \
  psql -U $DB_USER -d $DB_NAME -c "$QUERY"

