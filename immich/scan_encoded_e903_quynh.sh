#!/bin/bash

cd /media/Data/@mydata/Images/encoded-video/e9038fc0-e1ac-48f6-84ac-17838f91b108

for dir in */; do
  [ -d "$dir" ] && find "$dir" -maxdepth 99 -type f \( -iname "*.mp4" -o -iname "*.heic" \) |
  while read -r file; do
    if [[ "${file,,}" == *.mp4 ]]; then
      # Redirect all output and run in subshell to prevent any interference
      if (ffmpeg -v quiet -stats -threads "$(nproc)" -i "$file" -f null - </dev/null >/dev/null 2>&1); then
        echo "✅ Success: $file"
      else
        echo "❌ Failed:  $file - REMOVING"
        # mv "$file" "/media/Data/@archived/corrupted/encoded-video/"
      fi
    elif [[ "${file,,}" == *.heic ]]; then
      # Same approach for identify
      if (identify "$file" </dev/null >/dev/null 2>&1); then
        echo "✅ Success: $file"
      else
        echo "❌ Failed:  $file - REMOVING"
        # mv "$file" "/media/Data/@archived/corrupted/encoded-video/"
      fi
    fi
  done
done