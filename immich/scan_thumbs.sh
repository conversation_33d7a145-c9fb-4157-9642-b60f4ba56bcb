#!/bin/bash

CSV_FILE="data-1749053363004.csv"
COPY=false

[[ "$*" == *--copy* ]] && COPY=true
[[ "$COPY" == false ]] && echo "ℹ️ Dry run mode (use --copy to actually copy files)"

# Process tab-separated CSV (skip header)
tail -n +2 "$CSV_FILE" | while IFS=$',' read -r id originalPath; do
    # Clean inputs
    id=$(echo "$id" | tr -d '\r' | tr -d '"' | xargs)
    dst_dir=$(echo "$originalPath" | tr -d '\r' | tr -d '"' | xargs)
    filename=$(basename "$dst_dir")

    # Build source image path
    xx=${id:0:2}
    yy=${id:2:2}
    src="/media/Data/@mydata/Images/thumbs/9c08a44f-3a09-4dce-a446-5a700f1b2e9e/${xx}/${yy}/${id}-preview.jpeg"
    dst="$dst_dir"

    echo "🔍 Checking: '$src' → '$dst'"

    if [[ ! -f "$src" ]]; then
        echo "❌ Missing: $src"
        continue
    fi

    # Check image integrity
    identify "$src" >/dev/null 2>&1
    TEST_RESULT=$?

    if [[ "$TEST_RESULT" != 0 ]]; then
        echo "❌ Corrupted: $src"
        continue
    fi

    echo "✅ Valid: $src"

    if [[ "$COPY" == true ]]; then
        mkdir -p "$(dirname "$dst")"
        cp "$src" "$dst"
        echo "📁 Copied → $dst"
    else
        echo "📁 [Would copy \"$src\" → \"$dst\"]"
    fi
done