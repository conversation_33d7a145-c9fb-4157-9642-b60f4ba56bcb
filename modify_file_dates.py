#!/usr/bin/env python3

import os
import re
import datetime
from pathlib import Path
import argparse

def parse_date_from_folder_name(folder_name):
    """
    Parse date from folder name. Supports various formats:
    - YYYY-MM-DD
    - YYYY_MM_DD
    - YYYYMMDD
    - DD-MM-YYYY
    - DD_MM_YYYY
    - MM-DD-YYYY
    - MM_DD_YYYY
    """
    
    # Remove common prefixes/suffixes that might interfere
    clean_name = folder_name.strip()
    
    # Pattern for YYYY-MM-DD or YYYY_MM_DD
    pattern1 = r'(\d{4})[-_](\d{1,2})[-_](\d{1,2})'
    match = re.search(pattern1, clean_name)
    if match:
        year, month, day = match.groups()
        try:
            return datetime.date(int(year), int(month), int(day))
        except ValueError:
            pass
    
    # Pattern for YYYYMMDD
    pattern2 = r'(\d{8})'
    match = re.search(pattern2, clean_name)
    if match:
        date_str = match.group(1)
        try:
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
            return datetime.date(year, month, day)
        except ValueError:
            pass
    
    # Pattern for DD-MM-YYYY or DD_MM_YYYY
    pattern3 = r'(\d{1,2})[-_](\d{1,2})[-_](\d{4})'
    match = re.search(pattern3, clean_name)
    if match:
        day, month, year = match.groups()
        try:
            return datetime.date(int(year), int(month), int(day))
        except ValueError:
            pass
    
    # Pattern for MM-DD-YYYY or MM_DD_YYYY (assuming US format if day > 12)
    if match:
        first, second, year = match.groups()
        try:
            # Try DD-MM first
            if int(first) <= 12 and int(second) <= 31:
                return datetime.date(int(year), int(first), int(second))
            # Try MM-DD if first attempt fails
            elif int(second) <= 12 and int(first) <= 31:
                return datetime.date(int(year), int(second), int(first))
        except ValueError:
            pass
    
    return None

def modify_file_dates(directory_path, dry_run=True, recursive=True):
    """
    Modify file dates based on parent folder names.
    
    Args:
        directory_path: Path to the directory to process
        dry_run: If True, only show what would be changed without making changes
        recursive: If True, process subdirectories recursively
    """
    
    directory_path = Path(directory_path)
    
    if not directory_path.exists():
        print(f"❌ Directory not found: {directory_path}")
        return
    
    files_processed = 0
    files_modified = 0
    errors = 0
    
    print(f"🔍 Processing directory: {directory_path}")
    print(f"📋 Mode: {'DRY RUN' if dry_run else 'LIVE EXECUTION'}")
    print(f"🔄 Recursive: {recursive}")
    print("-" * 60)
    
    # Get all files to process
    if recursive:
        file_pattern = "**/*"
    else:
        file_pattern = "*"
    
    for file_path in directory_path.glob(file_pattern):
        if file_path.is_file():
            files_processed += 1
            
            # Get parent folder name
            parent_folder = file_path.parent.name
            
            # Parse date from folder name
            parsed_date = parse_date_from_folder_name(parent_folder)
            
            if parsed_date is None:
                print(f"⚠️  Could not parse date from folder: {parent_folder} (file: {file_path.name})")
                continue
            
            try:
                # Get current file stats
                current_stat = file_path.stat()
                current_mtime = datetime.datetime.fromtimestamp(current_stat.st_mtime)
                
                # Create new datetime with parsed date but keep original time
                new_datetime = datetime.datetime.combine(
                    parsed_date, 
                    current_mtime.time()
                )
                
                # Convert to timestamp
                new_timestamp = new_datetime.timestamp()
                
                print(f"📁 Folder: {parent_folder}")
                print(f"📄 File: {file_path.name}")
                print(f"📅 Parsed date: {parsed_date}")
                print(f"🕐 Current: {current_mtime.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"🕐 New:     {new_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
                
                if not dry_run:
                    # Modify both access time and modification time
                    os.utime(file_path, (new_timestamp, new_timestamp))
                    print("✅ Modified")
                    files_modified += 1
                else:
                    print("🔍 Would modify (dry run)")
                
                print("-" * 40)
                
            except Exception as e:
                print(f"❌ Error processing {file_path}: {e}")
                errors += 1
                print("-" * 40)
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    print(f"📁 Directory processed: {directory_path}")
    print(f"📄 Files processed: {files_processed}")
    if not dry_run:
        print(f"✅ Files modified: {files_modified}")
    else:
        print(f"🔍 Files that would be modified: {files_modified}")
    print(f"❌ Errors: {errors}")

def main():
    parser = argparse.ArgumentParser(
        description="Modify file dates based on their parent folder names",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Dry run (default) - see what would be changed
  python modify_file_dates.py /path/to/directory

  # Actually modify files
  python modify_file_dates.py /path/to/directory --execute

  # Process only current directory (not recursive)
  python modify_file_dates.py /path/to/directory --no-recursive

Supported date formats in folder names:
  - YYYY-MM-DD (e.g., 2023-12-25)
  - YYYY_MM_DD (e.g., 2023_12_25)
  - YYYYMMDD (e.g., 20231225)
  - DD-MM-YYYY (e.g., 25-12-2023)
  - DD_MM_YYYY (e.g., 25_12_2023)
        """
    )
    
    parser.add_argument(
        "directory",
        help="Directory path to process"
    )
    
    parser.add_argument(
        "--execute",
        action="store_true",
        help="Actually modify files (default is dry run)"
    )
    
    parser.add_argument(
        "--no-recursive",
        action="store_true",
        help="Don't process subdirectories recursively"
    )
    
    args = parser.parse_args()
    
    # Confirm execution if not dry run
    if args.execute:
        response = input(f"\n⚠️  You are about to modify file dates in: {args.directory}\nAre you sure? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("❌ Operation cancelled")
            return
    
    modify_file_dates(
        directory_path=args.directory,
        dry_run=not args.execute,
        recursive=not args.no_recursive
    )

if __name__ == "__main__":
    main()
