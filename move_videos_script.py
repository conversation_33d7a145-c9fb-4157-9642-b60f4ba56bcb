#!/usr/bin/env python3
"""
Script to copy videos from encoded paths to their original paths.
Reads from move_videos.txt and searches corresponding originalPath in CSV file.

Usage:
    python move_videos_script.py                    # Dry run (shows what would be copied)
    python move_videos_script.py exec               # Actually copy the files (skip existing)
    python move_videos_script.py exec --force       # Actually copy the files (overwrite existing)
"""

import csv
import os
import sys
import shutil
from pathlib import Path

def load_csv_mapping(csv_file):
    """Load the CSV file and create a mapping from encodedVideoPath to originalPath."""
    mapping = {}
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                encoded_path = row.get('encodedVideoPath', '').strip()
                original_path = row.get('originalPath', '').strip()
                
                # Only add to mapping if both paths exist and encoded path is not empty
                if encoded_path and original_path:
                    mapping[encoded_path] = original_path
                    
    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file}' not found!")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)
        
    return mapping

def read_video_paths(video_list_file):
    """Read the list of video paths from the text file."""
    try:
        with open(video_list_file, 'r', encoding='utf-8') as f:
            paths = [line.strip() for line in f if line.strip()]
        return paths
    except FileNotFoundError:
        print(f"Error: Video list file '{video_list_file}' not found!")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading video list file: {e}")
        sys.exit(1)

def copy_file_safe(src, dst, dry_run=True, force_overwrite=False):
    """Copy file from src to dst, creating directories as needed."""
    if dry_run:
        if os.path.exists(dst) and not force_overwrite:
            print(f"[DRY RUN] Would skip (exists): {src} -> {dst}")
        else:
            action = "overwrite" if os.path.exists(dst) else "copy"
            print(f"[DRY RUN] Would {action}: {src} -> {dst}")
        return True

    try:
        # Create destination directory if it doesn't exist
        dst_dir = os.path.dirname(dst)
        os.makedirs(dst_dir, exist_ok=True)

        # Check if source file exists
        if not os.path.exists(src):
            print(f"[ERROR] Source file does not exist: {src}")
            return False

        # Check if destination already exists
        if os.path.exists(dst) and not force_overwrite:
            print(f"[WARNING] Destination already exists, skipping: {dst}")
            return False

        # Copy the file
        shutil.copy2(src, dst)
        action = "Overwritten" if os.path.exists(dst) else "Copied"
        print(f"[SUCCESS] {action}: {src} -> {dst}")
        return True

    except Exception as e:
        print(f"[ERROR] Failed to copy {src} -> {dst}: {e}")
        return False

def main():
    # Configuration
    video_list_file = "move_videos.txt"
    csv_file = "immich/data-1750674776329.csv"

    # Parse command line arguments
    dry_run = True
    force_overwrite = False

    if len(sys.argv) > 1 and sys.argv[1].lower() == "exec":
        dry_run = False
        if len(sys.argv) > 2 and sys.argv[2].lower() == "--force":
            force_overwrite = True
            print("=== EXECUTION MODE (FORCE OVERWRITE) ===")
        else:
            print("=== EXECUTION MODE ===")
    else:
        print("=== DRY RUN MODE ===")
        print("Use 'python move_videos_script.py exec' to actually copy files")
        print("Use 'python move_videos_script.py exec --force' to overwrite existing files")

    print()
    
    # Load the CSV mapping
    print("Loading CSV mapping...")
    csv_mapping = load_csv_mapping(csv_file)
    print(f"Loaded {len(csv_mapping)} mappings from CSV")
    
    # Read the video paths to process
    print("Reading video paths...")
    video_paths = read_video_paths(video_list_file)
    print(f"Found {len(video_paths)} video paths to process")
    
    print("\n" + "="*80)
    
    # Process each video path
    found_count = 0
    not_found_count = 0
    success_count = 0
    
    for i, encoded_path in enumerate(video_paths, 1):
        print(f"\n[{i}/{len(video_paths)}] Processing: {encoded_path}")
        
        if encoded_path in csv_mapping:
            original_path = csv_mapping[encoded_path]
            found_count += 1
            
            print(f"  Found mapping -> {original_path}")
            
            if copy_file_safe(encoded_path, original_path, dry_run, force_overwrite):
                success_count += 1
        else:
            not_found_count += 1
            print(f"  [NOT FOUND] No mapping found in CSV for this path")
    
    # Summary
    print("\n" + "="*80)
    print("SUMMARY:")
    print(f"  Total videos processed: {len(video_paths)}")
    print(f"  Mappings found: {found_count}")
    print(f"  Mappings not found: {not_found_count}")
    
    if dry_run:
        print(f"  Would copy: {success_count} files")
        print("\nTo actually copy the files, run:")
        print("  python move_videos_script.py exec                # Skip existing files")
        print("  python move_videos_script.py exec --force        # Overwrite existing files")
    else:
        action = "copied/overwritten" if force_overwrite else "copied"
        print(f"  Successfully {action}: {success_count} files")
        print(f"  Failed/skipped: {found_count - success_count} files")

if __name__ == "__main__":
    main()
