#!/usr/bin/env python3
"""
Script to set file dates to a specific date while preserving the time component.
Reads file paths from a CSV file and updates their modification and access times.
"""

import os
import sys
import csv
import argparse
from datetime import datetime, time, timedelta
from pathlib import Path


def parse_csv_file(csv_file_path):
    """Parse CSV file and extract file paths."""
    file_paths = []
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            # Try to detect if it has headers
            sample = csvfile.read(1024)
            csvfile.seek(0)
            
            # Check if first line looks like a header
            first_line = csvfile.readline().strip()
            csvfile.seek(0)
            
            if first_line.lower().startswith('"originalpath"') or first_line.lower().startswith('originalpath'):
                # Skip header
                next(csvfile)
            else:
                csvfile.seek(0)
            
            reader = csv.reader(csvfile)
            for row in reader:
                if row and len(row) > 0:
                    # Remove quotes if present and get the file path
                    file_path = row[0].strip('"').strip("'")
                    if file_path and os.path.exists(file_path):
                        file_paths.append(file_path)
                    elif file_path:
                        print(f"⚠️  Warning: File not found: {file_path}")
    
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")
        return []
    
    return file_paths


def set_file_date(file_path, target_date, dry_run=True):
    """
    Set file's modification and access time to target date while preserving time component.
    
    Args:
        file_path (str): Path to the file
        target_date (datetime.date): Target date to set
        dry_run (bool): If True, only show what would be done
    
    Returns:
        bool: True if successful (or would be successful in dry run)
    """
    try:
        # Get current file stats
        stat_info = os.stat(file_path)
        current_mtime = datetime.fromtimestamp(stat_info.st_mtime)
        current_atime = datetime.fromtimestamp(stat_info.st_atime)
        
        # Create new datetime with target date but preserve original time
        new_mtime = datetime.combine(target_date, current_mtime.time())
        new_atime = datetime.combine(target_date, current_atime.time())
        
        # Convert to timestamps
        new_mtime_timestamp = new_mtime.timestamp()
        new_atime_timestamp = new_atime.timestamp()
        
        if dry_run:
            print(f"📄 File: {os.path.basename(file_path)}")
            print(f"📁 Path: {file_path}")
            print(f"🕐 Current mtime: {current_mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🕐 New mtime:     {new_mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🔍 Would modify (dry run)")
            print("-" * 50)
        else:
            # Actually modify the file times
            os.utime(file_path, (new_atime_timestamp, new_mtime_timestamp))
            print(f"📄 File: {os.path.basename(file_path)}")
            print(f"📁 Path: {file_path}")
            print(f"🕐 New mtime: {new_mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"✅ Modified successfully")
            print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        print("-" * 50)
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Set file dates to a specific date while preserving time component",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Dry run (default) - show what would be changed
  python set_file_dates.py immich/data-1750870775676.csv 2022-08-13
  
  # Actually modify the files
  python set_file_dates.py immich/data-1750870775676.csv 2022-08-13 --execute
  
  # Process individual files instead of CSV
  python set_file_dates.py --files /path/to/file1.jpg /path/to/file2.jpg --date 2022-08-13 --execute
        """
    )
    
    parser.add_argument('csv_file', nargs='?',
                       help='CSV file containing file paths in first column')
    parser.add_argument('target_date', nargs='?',
                       help='Target date in YYYY-MM-DD format (e.g., 2022-08-13)')
    parser.add_argument('--files', nargs='+',
                       help='Individual file paths to process (alternative to CSV)')
    parser.add_argument('--date',
                       help='Target date when using --files option')
    parser.add_argument('--execute', action='store_true',
                       help='Actually modify files (default is dry run)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Determine input mode and validate arguments
    if args.files and args.date:
        # Individual files mode
        file_paths = []
        target_date_str = args.date
        
        for file_path in args.files:
            if os.path.exists(file_path):
                file_paths.append(file_path)
            else:
                print(f"⚠️  Warning: File not found: {file_path}")
                
    elif args.csv_file and args.target_date:
        # CSV file mode
        if not os.path.exists(args.csv_file):
            print(f"❌ Error: CSV file not found: {args.csv_file}")
            sys.exit(1)
        
        print(f"📋 Reading file paths from CSV: {args.csv_file}")
        file_paths = parse_csv_file(args.csv_file)
        target_date_str = args.target_date
        
        if not file_paths:
            print("❌ No valid file paths found in CSV file.")
            sys.exit(1)
    else:
        print("❌ Error: Invalid arguments. Use either:")
        print("  1. CSV file and date: python set_file_dates.py file.csv 2022-08-13")
        print("  2. Individual files: python set_file_dates.py --files file1.jpg file2.jpg --date 2022-08-13")
        sys.exit(1)
    
    # Parse target date
    try:
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
    except ValueError:
        print(f"❌ Error: Invalid date format '{target_date_str}'. Use YYYY-MM-DD format.")
        sys.exit(1)
    
    if not file_paths:
        print("❌ No valid files to process.")
        sys.exit(1)
    
    # Show summary
    dry_run = not args.execute
    mode = "🔍 DRY RUN" if dry_run else "⚡ EXECUTE"
    
    print(f"\n{'='*60}")
    print(f"📅 FILE DATE MODIFICATION - {mode} MODE")
    print(f"{'='*60}")
    print(f"🎯 Target date: {target_date.strftime('%Y-%m-%d')}")
    print(f"📊 Files to process: {len(file_paths)}")
    
    if dry_run:
        print(f"\n💡 NOTE: This is a DRY RUN. No files will be modified.")
        print(f"   Use --execute flag to actually modify files.")
    else:
        # Confirm execution
        response = input(f"\n⚠️  You are about to modify {len(file_paths)} files.\n   Are you sure? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("❌ Operation cancelled")
            sys.exit(0)
    
    print(f"\n{'-'*60}\n")
    
    # Process files
    success_count = 0
    error_count = 0
    
    for i, file_path in enumerate(file_paths, 1):
        print(f"[{i}/{len(file_paths)}]")
        if set_file_date(file_path, target_date, dry_run):
            success_count += 1
        else:
            error_count += 1
    
    # Summary
    print("="*60)
    print("📊 SUMMARY")
    print("="*60)
    print(f"✅ Successfully processed: {success_count}")
    print(f"❌ Errors: {error_count}")
    print(f"📊 Total files: {len(file_paths)}")
    
    if dry_run and success_count > 0:
        print(f"\n💡 To actually modify the files, run with --execute flag:")
        if args.csv_file:
            print(f"   python {os.path.basename(sys.argv[0])} {args.csv_file} {target_date_str} --execute")
        else:
            print(f"   python {os.path.basename(sys.argv[0])} --files {' '.join(args.files)} --date {target_date_str} --execute")


if __name__ == "__main__":
    main()
